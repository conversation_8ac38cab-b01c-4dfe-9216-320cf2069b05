{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "productName": "Time Tracker", "version": "0.1.0", "identifier": "com.timetracker.app", "build": {"beforeBuildCommand": "npm run build", "beforeDevCommand": "npm run dev", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "app": {"windows": [{"title": "Time Tracker", "width": 1620, "height": 1000, "minWidth": 800, "minHeight": 500, "resizable": true, "fullscreen": false}], "trayIcon": {"iconPath": "icons/32x32.png", "iconAsTemplate": true, "menuOnLeftClick": true, "tooltip": "Time Tracker"}, "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}